name: Create GitHub Release
description: 'Create the release on GitHub with a changelog'
inputs:
  milestone:
    description: 'Name of the GitHub milestone for which a release will be created'
    required: true
  pre-release:
    description: 'Whether the release is a pre-release (a milestone or release candidate)'
    required: false
    default: 'false'
  token:
    description: 'Token to use for authentication with GitHub'
    required: true
  commercial:
    description: 'Whether to generate the changelog for the commercial release'
    required: true
runs:
  using: composite
  steps:
    - name: Generate Changelog
      uses: spring-io/github-changelog-generator@185319ad7eaa75b0e8e72e4b6db19c8b2cb8c4c1 #v0.0.11
      with:
        config-file: ${{ inputs.commercial && '.github/actions/create-github-release/changelog-generator-commercial.yml' || '.github/actions/create-github-release/changelog-generator-oss.yml' }}
        milestone: ${{ inputs.milestone }}
        token: ${{ inputs.token }}
    - name: Create GitHub Release
      shell: bash
      env:
        GITHUB_TOKEN: ${{ inputs.token }}
      run: gh release create ${{ format('v{0}', inputs.milestone) }} --notes-file changelog.md ${{ inputs.pre-release == 'true' && '--prerelease' || '' }}
