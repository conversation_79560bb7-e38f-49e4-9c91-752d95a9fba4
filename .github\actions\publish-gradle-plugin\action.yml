name: Publish Gradle Plugin
description: 'Publishes Spring Boot''s Gradle plugin to the Plugin Portal'
inputs:
  build-number:
    description: 'Build number to use when downloading plugin artifacts'
    required: false
    default: ${{ github.run_number }}
  gradle-plugin-publish-key:
    description: 'Gradle publishing key'
    required: true
  gradle-plugin-publish-secret:
    description: 'Gradle publishing secret'
    required: true
  jfrog-cli-config-token:
    description: 'Config token for the JFrog CLI'
    required: true
  plugin-version:
    description: 'Version of the plugin'
    required: true
runs:
  using: composite
  steps:
    - name: Set Up JFrog CLI
      uses: jfrog/setup-jfrog-cli@f748a0599171a192a2668afee8d0497f7c1069df # v4.5.6
      env:
        JF_ENV_SPRING: ${{ inputs.jfrog-cli-config-token }}
    - name: Download Artifacts
      shell: bash
      run: jf rt download --spec ${{ format('{0}/artifacts.spec', github.action_path) }} --spec-vars 'buildName=${{ format('spring-boot-{0}', inputs.plugin-version) }};buildNumber=${{ inputs.build-number }}'
    - name: Set Up Java
      uses: actions/setup-java@v4
      with:
        distribution: 'liberica'
        java-version: '17'
    - name: Publish
      shell: bash
      working-directory: ${{ github.action_path }}
      run: ${{ github.workspace }}/gradlew publishExisting -Pgradle.publish.key=${{ inputs.gradle-plugin-publish-key }} -Pgradle.publish.secret=${{ inputs.gradle-plugin-publish-secret }} -PbootVersion=${{ inputs.plugin-version }} -PrepositoryRoot=${{ github.workspace }}/repository
